import { RecordStatus } from '@common/enums';

export class IntegrationConfig {
  providerName: string;
}

export class PortfolioPreferences {
  integrationConfig?: IntegrationConfig;

  // Allow dynamic properties
  [key: string]: any;

  constructor(data?: Partial<PortfolioPreferences>) {
    if (data) {
      Object.assign(this, data);
    }
  }
}

export class CustomerPreferencesResponseDTO {
  customerId?: string;
  portfolio?: PortfolioPreferences;
  status: RecordStatus;
  createdAt: Date;
  updatedAt: Date;
}