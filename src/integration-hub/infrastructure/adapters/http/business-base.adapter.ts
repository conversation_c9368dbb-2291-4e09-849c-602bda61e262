import { BusinessBasePort } from "@integration-hub/infrastructure/ports/http/business-base.port";
import { CustomerPreferencesResponseDTO } from "@integration-hub/infrastructure/dto/out/customer-preferences-response.dto";

export class InfraBusinessBaseAdapter implements BusinessBasePort {
  async getCustomerPreferences(customerId: string): Promise<CustomerPreferencesResponseDTO> {
    return null;
  }
}