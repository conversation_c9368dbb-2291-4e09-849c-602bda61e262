import { Inject, Injectable } from '@nestjs/common';
import { PortfolioItemUpdatedRequestDTO } from '@integration-hub/application/dto/in/portfolio-item-updated-request.dto';
import { BusinessBasePort } from '@integration-hub/infrastructure/ports/http/business-base.port';
import { BusinessException, BusinessExceptionStatus } from '@common/exception/types/BusinessException';
import { IntegrationConfig } from '@integration-hub/infrastructure/dto/out/customer-preferences-response.dto';

@Injectable()
export class IntegrationUseCase {
  constructor(
    @Inject('BusinessBasePort')
    private readonly businessBaseAdapter: BusinessBasePort,
  ) {}

  async executeIntegrationStrategy(portfolioItemUpdatedRequestDTO: PortfolioItemUpdatedRequestDTO): Promise<any> {
    //1- Get customer preferences
    //2- Select correct strategy according customer preferences
    const customerPreferences = await this.businessBaseAdapter.getCustomerPreferences(portfolioItemUpdatedRequestDTO.customerId);
    if (!customerPreferences.portfolio.integrationConfig) {
      throw new BusinessException(
        'IntegrationUseCase',
        'No integration config found',
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    //TODO: implement strategy pattern here...
    const integrationConfig = customerPreferences.portfolio.integrationConfig;
    return true
  }
}