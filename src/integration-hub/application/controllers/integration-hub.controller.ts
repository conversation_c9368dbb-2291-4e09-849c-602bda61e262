import { Controller, Get, Post, Version, Body } from '@nestjs/common';
import { IntegrationUseCase } from '@integration-hub/application/use-cases/integration.use-case';
import { PortfolioItemUpdatedRequestDTO } from '@integration-hub/application/dto/in/portfolio-item-updated-request.dto';
import { ExcludeGuards } from '@common/auth/decorators/exclude-guard.decorator';
import { IntegrationOperations } from '@integration-hub/_docs/public-api/integration.operations';

//TODO: implement propert auth way for server to server / event driven
@ExcludeGuards('AuthnGuard', 'AuthzAccountGuard', 'AuthzUserInAccountGuard')
@Controller('integration-hub')
export class IntegrationHubController {
  constructor(private readonly integrationUseCase: IntegrationUseCase) {}

  @IntegrationOperations.executeIntegration()
  @Post('/execute')
  @Version('1')
  async execute(@Body() portfolioItemUpdatedRequestDTO: PortfolioItemUpdatedRequestDTO): Promise<any> {
    const result = await this.integrationUseCase.executeIntegrationStrategy(portfolioItemUpdatedRequestDTO);
    return {
      statusCode: 200,
      data: result,
    }
  }
}