import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { IntegrationHubController } from '@integration-hub/application/controllers/integration-hub.controller';
import { IntegrationUseCase } from '@integration-hub/application/use-cases/integration.use-case';
import { InfraBusinessBaseAdapter } from '@integration-hub/infrastructure/adapters/http/business-base.adapter';

const httpModule = HttpModule.registerAsync({
  useFactory: () => ({
    timeout: 180000,
    maxRedirects: 5,
  }),
});

@Module({
  imports: [
    httpModule,
  ],
  providers: [
    IntegrationUseCase,
    {
      provide: 'BusinessBasePort',
      useClass: InfraBusinessBaseAdapter,
    },
  ],
  controllers: [
    IntegrationHubController,
  ],
})
export class IntegrationHubModule { }
